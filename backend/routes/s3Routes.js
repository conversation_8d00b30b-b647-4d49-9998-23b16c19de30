import express from 'express';
import {
    testS3Route,
    generatePresignedUrl,
    generatePdfViewUrl,
    generateBatchPresignedUrls
} from '../controllers/s3Controller.js';

const router = express.Router();

// Test route to verify S3 routes are working
router.get('/test', testS3Route);
router.post('/presigned-url', generatePresignedUrl);
router.post('/pdf-view-url', generatePdfViewUrl);
router.post('/batch-presigned-urls', generateBatchPresignedUrls);

export default router;
