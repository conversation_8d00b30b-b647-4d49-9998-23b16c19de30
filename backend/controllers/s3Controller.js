import { S3 } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';

// Configure AWS S3
const s3 = new S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || 'ap-south-1',
});

/**
 * Test endpoint to verify S3 routes are working
 */
export const testS3Route = async (req, res) => {
    try {
        res.json({
            success: true,
            message: 'S3 routes are working',
            userId: req.id,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'S3 test failed',
            error: error.message
        });
    }
};

/**
 * Generate presigned URL for S3 operations
 */
export const generatePresignedUrl = async (req, res) => {
    try {
        console.log('S3 presigned URL request received:', {
            body: req.body,
            userId: req.id,
            headers: {
                authorization: req.headers.authorization ? 'Bearer [REDACTED]' : 'None',
                'content-type': req.headers['content-type']
            }
        });

        const { s3Key, operation = 'getObject', expiresIn = 3600 } = req.body;

        if (!s3Key) {
            console.log('S3 key missing in request');
            return res.status(400).json({
                success: false,
                message: 'S3 key is required'
            });
        }

        const bucketName = process.env.UPLOAD_BUCKET_NAME || process.env.S3_BUCKET_NAME || 'user-uploads-aegis-scholar';
        
        if (!bucketName) {
            return res.status(500).json({
                success: false,
                message: 'S3 bucket configuration missing'
            });
        }

        let command;
        
        switch (operation) {
            case 'getObject':
                command = new GetObjectCommand({
                    Bucket: bucketName,
                    Key: s3Key,
                    ResponseContentType: 'application/pdf',
                    ResponseContentDisposition: 'inline'
                });
                break;
            case 'putObject':
                command = new PutObjectCommand({
                    Bucket: bucketName,
                    Key: s3Key,
                    ContentType: req.body.contentType || 'application/pdf',
                });
                break;
            default:
                return res.status(400).json({
                    success: false,
                    message: 'Invalid operation. Supported: getObject, putObject'
                });
        }

        const presignedUrl = await getSignedUrl(s3, command, {
            expiresIn: parseInt(expiresIn)
        });

        console.log(`Generated presigned URL for ${s3Key}:`, presignedUrl.substring(0, 100) + '...');

        res.json({
            success: true,
            url: presignedUrl,
            expiresIn: expiresIn,
            operation: operation
        });

    } catch (error) {
        console.error('Error generating presigned URL:', {
            error: error.message,
            stack: error.stack,
            s3Key: req.body.s3Key,
            userId: req.id
        });
        res.status(500).json({
            success: false,
            message: 'Failed to generate presigned URL',
            error: error.message,
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
};

/**
 * Generate presigned URL specifically for PDF viewing
 */
export const generatePdfViewUrl = async (req, res) => {
    try {
        const { s3Key } = req.body;

        if (!s3Key) {
            return res.status(400).json({
                success: false,
                message: 'S3 key is required'
            });
        }

        const bucketName = process.env.UPLOAD_BUCKET_NAME || process.env.S3_BUCKET_NAME || 'user-uploads-aegis-scholar';
        
        const command = new GetObjectCommand({
            Bucket: bucketName,
            Key: s3Key,
            ResponseContentType: 'application/pdf',
            ResponseContentDisposition: 'inline' // Display in browser instead of download
        });

        const presignedUrl = await getSignedUrl(s3, command, {
            expiresIn: 3600 // 1 hour
        });

        console.log(`Generated PDF view URL for ${s3Key}:`, presignedUrl.substring(0, 100) + '...');

        res.json({
            success: true,
            url: presignedUrl,
            expiresIn: 3600
        });

    } catch (error) {
        console.error('Error generating PDF view URL:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate PDF view URL',
            error: error.message
        });
    }
};

/**
 * Batch generate presigned URLs for multiple files
 */
export const generateBatchPresignedUrls = async (req, res) => {
    try {
        const { s3Keys, operation = 'getObject', expiresIn = 3600 } = req.body;

        if (!Array.isArray(s3Keys) || s3Keys.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Array of S3 keys is required'
            });
        }

        const bucketName = process.env.UPLOAD_BUCKET_NAME || process.env.S3_BUCKET_NAME || 'user-uploads-aegis-scholar';
        
        const urls = await Promise.all(
            s3Keys.map(async (s3Key) => {
                try {
                    let command;
                    
                    if (operation === 'getObject') {
                        command = new GetObjectCommand({
                            Bucket: bucketName,
                            Key: s3Key,
                            ResponseContentType: 'application/pdf',
                            ResponseContentDisposition: 'inline'
                        });
                    } else {
                        command = new PutObjectCommand({
                            Bucket: bucketName,
                            Key: s3Key,
                        });
                    }

                    const presignedUrl = await getSignedUrl(s3, command, {
                        expiresIn: parseInt(expiresIn)
                    });

                    return {
                        s3Key,
                        url: presignedUrl,
                        success: true
                    };
                } catch (error) {
                    return {
                        s3Key,
                        error: error.message,
                        success: false
                    };
                }
            })
        );

        res.json({
            success: true,
            urls: urls,
            expiresIn: expiresIn
        });

    } catch (error) {
        console.error('Error generating batch presigned URLs:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate batch presigned URLs',
            error: error.message
        });
    }
};
