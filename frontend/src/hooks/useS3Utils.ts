import { useCallback } from 'react';
import { useAxiosPrivate } from './useAxiosPrivate';

// Cache for presigned URLs to avoid repeated API calls
const presignedUrlCache = new Map<string, { url: string; expiry: number }>();
const CACHE_DURATION = 50 * 60 * 1000; // 50 minutes (URLs expire in 1 hour)

export const useS3Utils = () => {
    const axiosPrivate = useAxiosPrivate();

    const getPresignedUrl = useCallback(async (s3Key: string): Promise<string> => {
        if (!s3Key) return '';
        
        // If it's already a full URL, return as is
        if (s3Key.startsWith('http://') || s3Key.startsWith('https://')) {
            return s3Key;
        }
        
        // Check cache first
        const cached = presignedUrlCache.get(s3Key);
        if (cached && Date.now() < cached.expiry) {
            return cached.url;
        }
        
        try {
            console.log('Requesting presigned URL for s3Key:', s3Key);
            console.log('Using axiosPrivate instance:', !!axiosPrivate);

            // Get presigned URL from backend using authenticated axios
            const response = await axiosPrivate.post('/api/s3/presigned-url', {
                s3Key: s3Key,
                operation: 'getObject'
            });

            console.log('Presigned URL response:', response.status, response.data);
            const presignedUrl = response.data.url;
            
            // Cache the URL
            presignedUrlCache.set(s3Key, {
                url: presignedUrl,
                expiry: Date.now() + CACHE_DURATION
            });
            
            return presignedUrl;
        } catch (error: any) {
            console.error('Error getting presigned URL:', {
                message: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                s3Key: s3Key
            });

            // Throw the error instead of falling back, so we can debug it
            throw error;
        }
    }, [axiosPrivate]);

    const createPdfViewerUrl = useCallback(async (pdfUrl: string): Promise<string> => {
        if (!pdfUrl) return '';
        return await getPresignedUrl(pdfUrl);
    }, [getPresignedUrl]);

    const downloadPdfFromS3 = useCallback(async (s3Key: string, filename?: string): Promise<void> => {
        try {
            const url = await getPresignedUrl(s3Key);
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`Failed to download PDF: ${response.statusText}`);
            }
            
            const blob = await response.blob();
            const downloadUrl = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = filename || s3Key.split('/').pop() || 'document.pdf';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            URL.revokeObjectURL(downloadUrl);
        } catch (error) {
            console.error('Error downloading PDF:', error);
            throw error;
        }
    }, [getPresignedUrl]);

    const isPdfUrl = useCallback((url: string): boolean => {
        if (!url) return false;
        return url.toLowerCase().includes('.pdf') || url.toLowerCase().includes('application/pdf');
    }, []);

    const testS3Route = useCallback(async (): Promise<any> => {
        try {
            console.log('Testing S3 route accessibility...');
            const response = await axiosPrivate.get('/api/s3/test');
            console.log('S3 test route response:', response.data);
            return response.data;
        } catch (error: any) {
            console.error('S3 test route failed:', error);
            throw error;
        }
    }, [axiosPrivate]);

    return {
        getPresignedUrl,
        createPdfViewerUrl,
        downloadPdfFromS3,
        isPdfUrl,
        testS3Route
    };
};
