import React from 'react';
import { createBrowserRouter, RouterProvider, Route, createRoutesFromElements, Outlet } from 'react-router-dom';
import { AnalyticsProvider } from './contexts/analyticsContext';
import AnalyticsConsentBanner from './components/AnalyticsConsentBanner';
import Homepage from './pages/Homepage';
import Login from './pages/Login';
import Register from './pages/Register';
import StudentDashboard from './pages/StudentDashboard';
import PracticeTestSetup from './pages/PracticeTestSetupDialog';
import Layout from './components/Layout';
import StudentTest from './pages/StudentTestPage';
import TestInstructionsPage from './pages/TestInstructionsPage';
import ProfilePage from './pages/ProfilePage';
import TeacherDashboard from './pages/TeacherDashboard';
import ClassDetails from './pages/ClassDetails';
import ScheduleTestForm from './pages/ScheduleTestPage';
import ReviewTestPage from './pages/ReviewTestPage';
import Analytics from './pages/Analytics';
import TestResults from './pages/TestResults';
import TestSubmissions from './pages/TestSubmissions';
import SubjectDetailsPage from './pages/SubjectDetailsPage';
import 'react-toastify/dist/ReactToastify.css';
import EmailVerificationPage from './pages/EmailVerificationPage';
import ResetPassword from './pages/ResetPassword';
import AegisGrader from './pages/AegisGrader';
import GradingDetails from './pages/GradingDetails';
import GradingSubmissionsPage from './pages/GradingSubmissionsPage';
import BillingDashboard from './pages/BillingDashboard';
import AegisAi from './pages/AegisAi';
import AdminPage from './pages/Admin';
import PrivacyPolicy from './pages/PrivacyPolicy';
import WelcomePage from './pages/WelcomePage';
import S3TestPage from './pages/S3TestPage';

// import './styles/fonts.css';



const router = createBrowserRouter(
  createRoutesFromElements(
    <Route path="/" element={<Root />}>
      {/* Routes without Sidebar */}
      <Route index element={<Homepage />} />
      <Route path="login" element={<Login />} />
      <Route path="register" element={<Register />} />
      <Route path="test-instructions" element={<TestInstructionsPage/>} />
      <Route path="test" element={<StudentTest/>} />
      <Route path="verify-email" element={<EmailVerificationPage/>} />
      <Route path="reset-password" element={<ResetPassword/>} />
      <Route path="platform-admin" element={<AdminPage />} />
      <Route path="privacy-policy" element={<PrivacyPolicy />} />
      <Route path="welcome" element={<WelcomePage />} />
      <Route path="s3-test" element={<S3TestPage />} />

      {/* Routes with Sidebar */}
      <Route element={<Layout />}>
        <Route path="aegis-grader" element={<AegisGrader />}></Route>
        <Route path="grading-submissions" element={<GradingSubmissionsPage />}></Route>
        <Route path="gradingDetails/:id" element={<GradingDetails />}></Route>
        <Route path="billing" element={<BillingDashboard />}></Route>
        {/* <Route index element={<AegisAi />} />
        <Route path="aegis-ai" element={<AegisAi />} />
        <Route path="student-dashboard" element={<StudentDashboard />} />
        <Route path="teacher-dashboard" element={<TeacherDashboard />} />
        <Route path="practice-setup" element={<PracticeTestSetup />} /> */}
        {/* <Route path="profile" element={<ProfilePage />} /> */}
        {/* <Route path="schedule-test" element={<ScheduleTestForm />} />
        <Route path='classDetails/:classId' element={<ClassDetails/>}></Route>
        <Route path="reviewTest" element={<ReviewTestPage />}></Route> */}
        <Route path="analytics" element={<Analytics/>}></Route>
        {/* <Route path="subjectDetails/:classId" element={<SubjectDetailsPage />}></Route>
        <Route path="test-results" element={<TestResults />}></Route>
        <Route path="test-submissions" element={<TestSubmissions />}></Route> */}
       
      </Route>
    </Route>
  )
);

function Root() {
  return (
    <AnalyticsProvider>
      <Outlet />
      <AnalyticsConsentBanner />
    </AnalyticsProvider>
  );
}

const App: React.FC = () => {
  return <RouterProvider router={router} />;
};

export default App;
