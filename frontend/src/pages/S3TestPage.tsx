import React from 'react';
import S3ConnectivityTest from '@/components/S3ConnectivityTest';

const S3TestPage: React.FC = () => {
    return (
        <div className="min-h-screen bg-background p-6">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-2xl font-bold mb-6 text-foreground">S3 Connectivity Testing</h1>
                
                <div className="space-y-6">
                    <S3ConnectivityTest />
                    
                    <div className="bg-card p-4 rounded-lg border border-border">
                        <h2 className="text-lg font-semibold mb-2 text-card-foreground">Instructions</h2>
                        <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                            <li>Click "Run Connectivity Test" to test S3 functionality</li>
                            <li>Check the browser console for detailed logs</li>
                            <li>The test will verify authentication, presigned URL generation, and URL accessibility</li>
                            <li>Results will be displayed below the button</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default S3TestPage;
