import React, { useState } from 'react';
import { useS3Utils } from '@/hooks/useS3Utils';

interface PdfDebugViewerProps {
    s3Key: string;
    title: string;
    className?: string;
    style?: React.CSSProperties;
}

const PdfDebugViewer: React.FC<PdfDebugViewerProps> = ({ s3Key, title, className, style }) => {
    const [debugInfo, setDebugInfo] = useState<any>({});
    const [loading, setLoading] = useState<boolean>(false);
    const { createPdfViewerUrl, testS3Route } = useS3Utils();

    const testPdfUrl = async () => {
        setLoading(true);
        try {
            console.log('Testing PDF URL for s3Key:', s3Key);

            // First test if S3 routes are accessible
            console.log('Testing S3 route accessibility...');
            const testResponse = await testS3Route();
            console.log('S3 test route response:', testResponse);

            const presignedUrl = await createPdfViewerUrl(s3Key);
            console.log('Generated presigned URL:', presignedUrl);
            
            // Test if URL is accessible
            const response = await fetch(presignedUrl, { method: 'HEAD' });
            console.log('URL test response:', response.status, response.statusText);
            
            const headers: any = {};
            response.headers.forEach((value, key) => {
                headers[key] = value;
            });
            
            setDebugInfo({
                s3Key,
                presignedUrl: presignedUrl.substring(0, 100) + '...',
                fullUrl: presignedUrl,
                status: response.status,
                statusText: response.statusText,
                headers,
                contentType: response.headers.get('content-type'),
                contentLength: response.headers.get('content-length'),
                timestamp: new Date().toISOString()
            });
            
        } catch (error) {
            console.error('Error testing PDF URL:', error);
            setDebugInfo({
                s3Key,
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            });
        } finally {
            setLoading(false);
        }
    };

    const testS3Connectivity = async () => {
        setLoading(true);
        try {
            const testResponse = await testS3Route();
            setDebugInfo({
                s3Key,
                testResult: testResponse,
                message: 'S3 route connectivity test successful',
                timestamp: new Date().toISOString()
            });
        } catch (error: any) {
            setDebugInfo({
                s3Key,
                error: error.message,
                response: error.response?.data,
                status: error.response?.status,
                message: 'S3 route connectivity test failed',
                timestamp: new Date().toISOString()
            });
        } finally {
            setLoading(false);
        }
    };

    const openInNewTab = () => {
        if (debugInfo.fullUrl) {
            window.open(debugInfo.fullUrl, '_blank');
        }
    };

    return (
        <div className={`p-4 border border-border rounded-lg ${className}`} style={style}>
            <h3 className="text-lg font-semibold mb-4">PDF Debug Viewer</h3>
            
            <div className="space-y-4">
                <div>
                    <p className="text-sm text-muted-foreground">S3 Key: {s3Key}</p>
                    <p className="text-sm text-muted-foreground">Title: {title}</p>
                </div>
                
                <div className="flex gap-2 flex-wrap">
                    <button
                        onClick={testS3Connectivity}
                        disabled={loading}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                        {loading ? 'Testing...' : 'Test S3 Route'}
                    </button>

                    <button
                        onClick={testPdfUrl}
                        disabled={loading}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
                    >
                        {loading ? 'Testing...' : 'Test PDF URL'}
                    </button>

                    {debugInfo.fullUrl && (
                        <button
                            onClick={openInNewTab}
                            className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80"
                        >
                            Open in New Tab
                        </button>
                    )}
                </div>
                
                {debugInfo.timestamp && (
                    <div className="bg-muted p-3 rounded-md">
                        <h4 className="font-medium mb-2">Debug Information:</h4>
                        <pre className="text-xs overflow-auto">
                            {JSON.stringify(debugInfo, null, 2)}
                        </pre>
                    </div>
                )}
                
                {debugInfo.fullUrl && (
                    <div className="space-y-2">
                        <h4 className="font-medium">Test Iframe:</h4>
                        <div className="border border-border rounded-md" style={{ height: '300px' }}>
                            <iframe
                                src={`${debugInfo.fullUrl}#toolbar=0&navpanes=0&scrollbar=0`}
                                title={title}
                                width="100%"
                                height="100%"
                                style={{ border: 'none' }}
                                onLoad={() => console.log('Debug iframe loaded successfully')}
                                onError={() => console.log('Debug iframe failed to load')}
                            />
                        </div>
                    </div>
                )}
                
                {debugInfo.fullUrl && (
                    <div className="space-y-2">
                        <h4 className="font-medium">Test Embed:</h4>
                        <div className="border border-border rounded-md" style={{ height: '300px' }}>
                            <embed
                                src={`${debugInfo.fullUrl}#toolbar=0&navpanes=0&scrollbar=0`}
                                type="application/pdf"
                                width="100%"
                                height="100%"
                                style={{ border: 'none' }}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default PdfDebugViewer;
