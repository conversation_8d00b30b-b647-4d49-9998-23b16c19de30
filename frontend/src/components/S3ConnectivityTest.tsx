import React, { useState } from 'react';
import { useS3Utils } from '@/hooks/useS3Utils';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';

interface S3ConnectivityTestProps {
    s3Key?: string;
}

const S3ConnectivityTest: React.FC<S3ConnectivityTestProps> = ({ 
    s3Key = "Sure_Shot_2024_GS_PAPER_GS_1_T_01_SHAKTI_DUBEY_AIR_01_9e6e373cbd.pdf-27531de0-8e69-47fa-a17d-86a6299f085a.pdf" 
}) => {
    const [testResults, setTestResults] = useState<any>({});
    const [loading, setLoading] = useState<boolean>(false);
    const { getPresignedUrl, testS3Route } = useS3Utils();
    const axiosPrivate = useAxiosPrivate();

    const runConnectivityTest = async () => {
        setLoading(true);
        const results: any = {
            timestamp: new Date().toISOString(),
            tests: {}
        };

        try {
            // Test 1: Basic S3 route test
            console.log('🧪 Test 1: Testing S3 route accessibility...');
            try {
                const s3TestResponse = await testS3Route();
                results.tests.s3RouteTest = {
                    success: true,
                    data: s3TestResponse,
                    message: 'S3 route accessible'
                };
                console.log('✅ S3 route test passed:', s3TestResponse);
            } catch (error: any) {
                results.tests.s3RouteTest = {
                    success: false,
                    error: error.message,
                    details: error.response?.data,
                    status: error.response?.status
                };
                console.error('❌ S3 route test failed:', error);
            }

            // Test 2: Direct axios call to presigned URL endpoint
            console.log('🧪 Test 2: Testing direct presigned URL request...');
            try {
                const directResponse = await axiosPrivate.post('/api/s3/presigned-url', {
                    s3Key: s3Key,
                    operation: 'getObject'
                });
                results.tests.directPresignedUrlTest = {
                    success: true,
                    data: directResponse.data,
                    message: 'Direct presigned URL request successful'
                };
                console.log('✅ Direct presigned URL test passed:', directResponse.data);
            } catch (error: any) {
                results.tests.directPresignedUrlTest = {
                    success: false,
                    error: error.message,
                    details: error.response?.data,
                    status: error.response?.status
                };
                console.error('❌ Direct presigned URL test failed:', error);
            }

            // Test 3: Using the hook's getPresignedUrl function (now returns proxy URL)
            console.log('🧪 Test 3: Testing hook getPresignedUrl function...');
            try {
                const proxyUrl = await getPresignedUrl(s3Key);
                results.tests.hookProxyUrlTest = {
                    success: true,
                    url: proxyUrl,
                    message: 'Hook proxy URL generation successful'
                };
                console.log('✅ Hook proxy URL test passed:', proxyUrl);

                // Test 4: Test if the proxy URL is accessible via authenticated request
                console.log('🧪 Test 4: Testing proxy URL accessibility...');
                try {
                    const urlTestResponse = await axiosPrivate.get(proxyUrl, {
                        responseType: 'blob',
                        timeout: 10000 // 10 second timeout
                    });
                    results.tests.proxyAccessibilityTest = {
                        success: urlTestResponse.status === 200,
                        status: urlTestResponse.status,
                        statusText: urlTestResponse.statusText,
                        contentType: urlTestResponse.headers['content-type'],
                        contentLength: urlTestResponse.data.size,
                        message: urlTestResponse.status === 200 ? 'Proxy URL is accessible and returns PDF content' : 'Proxy URL is not accessible'
                    };
                    console.log('✅ Proxy accessibility test completed:', urlTestResponse.status, 'Content size:', urlTestResponse.data.size);
                } catch (error: any) {
                    results.tests.proxyAccessibilityTest = {
                        success: false,
                        error: error.message,
                        status: error.response?.status,
                        statusText: error.response?.statusText,
                        message: 'Failed to test proxy URL accessibility'
                    };
                    console.error('❌ Proxy accessibility test failed:', error);
                }
            } catch (error: any) {
                results.tests.hookProxyUrlTest = {
                    success: false,
                    error: error.message,
                    details: error.response?.data,
                    status: error.response?.status
                };
                console.error('❌ Hook proxy URL test failed:', error);
            }

        } catch (error: any) {
            console.error('❌ Overall test failed:', error);
            results.overallError = error.message;
        }

        setTestResults(results);
        setLoading(false);
        console.log('🏁 All tests completed. Results:', results);
    };

    const clearResults = () => {
        setTestResults({});
    };

    return (
        <div className="p-6 bg-card rounded-lg border border-border">
            <h2 className="text-xl font-semibold mb-4 text-card-foreground">S3 Connectivity Test</h2>
            
            <div className="mb-4">
                <p className="text-sm text-muted-foreground mb-2">
                    Testing S3 connectivity with key: <code className="bg-muted px-1 rounded">{s3Key}</code>
                </p>
                
                <div className="flex gap-2">
                    <button
                        onClick={runConnectivityTest}
                        disabled={loading}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50"
                    >
                        {loading ? 'Running Tests...' : 'Run Connectivity Test'}
                    </button>
                    
                    <button
                        onClick={clearResults}
                        className="px-4 py-2 bg-secondary text-secondary-foreground rounded hover:bg-secondary/90"
                    >
                        Clear Results
                    </button>
                </div>
            </div>

            {Object.keys(testResults).length > 0 && (
                <div className="mt-6">
                    <h3 className="text-lg font-medium mb-3 text-card-foreground">Test Results</h3>
                    <pre className="bg-muted p-4 rounded text-sm overflow-auto max-h-96 text-muted-foreground">
                        {JSON.stringify(testResults, null, 2)}
                    </pre>
                </div>
            )}
        </div>
    );
};

export default S3ConnectivityTest;
