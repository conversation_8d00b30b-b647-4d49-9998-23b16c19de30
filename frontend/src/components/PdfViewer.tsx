import React, { useState, useEffect, useRef } from 'react';
import { useS3Utils } from '@/hooks/useS3Utils';

interface PdfViewerProps {
    s3Key: string;
    title: string;
    className?: string;
    style?: React.CSSProperties;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ s3Key, title, className, style }) => {
    const [pdfUrl, setPdfUrl] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string>('');
    const [useEmbedFallback, setUseEmbedFallback] = useState<boolean>(false);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const { createPdfViewerUrl } = useS3Utils();

    useEffect(() => {
        const loadPdfUrl = async () => {
            if (!s3Key) {
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                setError('');
                const url = await createPdfViewerUrl(s3Key);
                console.log('PDF URL loaded:', url);
                setPdfUrl(url);
            } catch (err) {
                console.error('Error loading PDF URL:', err);
                setError('Failed to load PDF');
            } finally {
                setLoading(false);
            }
        };

        loadPdfUrl();
    }, [s3Key, createPdfViewerUrl]);

    // Handle iframe load errors
    const handleIframeError = () => {
        console.log('Iframe failed to load, trying embed fallback');
        setUseEmbedFallback(true);
    };

    // Handle iframe load success
    const handleIframeLoad = () => {
        console.log('Iframe loaded successfully');
        setError('');
    };

    if (loading) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-sm text-muted-foreground">Loading PDF...</p>
                </div>
            </div>
        );
    }

    if (error && !pdfUrl) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <div className="text-center">
                    <p className="text-sm text-destructive mb-2">{error}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="text-xs text-primary hover:underline"
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    if (!pdfUrl) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <p className="text-sm text-muted-foreground">No PDF available</p>
            </div>
        );
    }

    // Try different approaches for PDF display
    if (useEmbedFallback) {
        return (
            <div className={className} style={style}>
                <embed
                    src={`${pdfUrl}#toolbar=0&navpanes=0&scrollbar=0`}
                    type="application/pdf"
                    width="100%"
                    height="100%"
                    style={{ border: 'none' }}
                />
            </div>
        );
    }

    return (
        <div className={className} style={style}>
            <iframe
                ref={iframeRef}
                src={`${pdfUrl}#toolbar=0&navpanes=0&scrollbar=0&view=FitH`}
                title={title}
                width="100%"
                height="100%"
                style={{ border: 'none' }}
                onLoad={handleIframeLoad}
                onError={handleIframeError}
                sandbox="allow-same-origin allow-scripts"
            />
        </div>
    );
};

export default PdfViewer;
