import { useMemo } from "react";

// --- Types for Question Breakdown ---
interface CriterionBreakdown {
    criterion: string;
    score: string;
    maxScore?: string;
}

interface QuestionBreakdown {
    questionNumber: string;
    marksAwarded: number;
    marksPossible: number;
    percentage: number;
    feedback: string;
    criteriaBreakdown: CriterionBreakdown[];
}

interface EvaluationBreakdown {
    totalMarks: number;
    maxMarks: number;
    overallPercentage: number;
    questions: QuestionBreakdown[];
}

// --- Question Breakdown Function ---
export const parseQuestionBreakdown = (evaluationData: any): EvaluationBreakdown | null => {
    try {
        // Handle different input formats
        let xmlString = '';

        if (Array.isArray(evaluationData) && evaluationData.length > 0) {
            // Current format: Array with markdown/XML string
            xmlString = evaluationData[0];
        } else if (typeof evaluationData === 'string') {
            // Direct XML string
            xmlString = evaluationData;
        } else {
            console.error('Invalid evaluation data format:', typeof evaluationData, evaluationData);
            return null;
        }

        // Extract XML content from markdown if needed
        const xmlMatch = xmlString.match(/<evaluation>([\s\S]*?)<\/evaluation>/);
        if (!xmlMatch) {
            console.error('No evaluation XML found in data');
            return null;
        }

        const xmlContent = `<evaluation>${xmlMatch[1]}</evaluation>`;

        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');

        if (xmlDoc.getElementsByTagName("parsererror").length > 0) {
            console.error('XML parsing error');
            return null;
        }

        const evaluation = xmlDoc.querySelector('evaluation');
        if (!evaluation) {
            console.error('No evaluation element found in parsed XML');
            return null;
        }

        // Extract overall evaluation data
        const totalMarks = parseInt(evaluation.querySelector('total_marks')?.textContent || '0');
        const maxMarks = parseInt(evaluation.querySelector('maximum_possible_marks')?.textContent || '0');
        const overallPercentage = parseInt(evaluation.querySelector('percentage_score')?.textContent || '0');

        // Extract question details
        const questionElements = evaluation.querySelectorAll('question');

        const questions: QuestionBreakdown[] = Array.from(questionElements).map(question => {
            const questionNumber = question.getAttribute('number') || question.querySelector('number')?.textContent || '';
            const marksAwarded = parseInt(question.querySelector('marks_awarded')?.textContent || '0');
            const marksPossible = parseInt(question.querySelector('marks_possible')?.textContent || '0');
            const percentage = marksPossible > 0 ? Math.round((marksAwarded / marksPossible) * 100) : 0;
            const feedback = question.querySelector('feedback')?.textContent?.trim() || '';

            // Extract criteria breakdown - simplified approach
            const marksBreakdown = question.querySelector('marks_breakdown');
            const criteriaBreakdown: CriterionBreakdown[] = [];

            if (marksBreakdown) {
                const criteria = Array.from(marksBreakdown.querySelectorAll('criterion'));

                criteria.forEach(criterion => {
                    const name = criterion.getAttribute('name') || '';
                    const score = criterion.textContent || '0';

                    // Skip "Total Possible" entries for now - keep it simple
                    if (!name.startsWith('Total Possible for ')) {
                        criteriaBreakdown.push({
                            criterion: name,
                            score: score,
                            maxScore: '0' // Will be updated if we find the corresponding total
                        });
                    }
                });

                // Try to match totals with criteria
                criteria.forEach(criterion => {
                    const name = criterion.getAttribute('name') || '';
                    const score = criterion.textContent || '0';

                    if (name.startsWith('Total Possible for ')) {
                        const baseName = name.replace('Total Possible for ', '');
                        const matchingCriterion = criteriaBreakdown.find(c => c.criterion === baseName);
                        if (matchingCriterion) {
                            matchingCriterion.maxScore = score;
                        }
                    }
                });
            }

            return {
                questionNumber,
                marksAwarded,
                marksPossible,
                percentage,
                feedback,
                criteriaBreakdown
            };
        });

        return {
            totalMarks,
            maxMarks,
            overallPercentage,
            questions
        };

    } catch (error) {
        console.error('Error parsing evaluation breakdown:', error);
        return null;
    }
};

// --- Display Component for Question Breakdown ---
const QuestionBreakdownDisplay: React.FC<{ evaluationData: EvaluationBreakdown | null }> = ({ evaluationData }) => {
    // Handle null or invalid data
    if (!evaluationData) {
        return (
            <div className="bg-card border border-border rounded-lg p-6 text-center">
                <p className="text-muted-foreground">Unable to load question breakdown data.</p>
            </div>
        );
    }
    const getPerformanceColor = (percentage: number): string => {
        if (percentage >= 80) return "text-emerald-600 dark:text-emerald-400";
        if (percentage >= 60) return "text-blue-600 dark:text-blue-400";
        if (percentage >= 40) return "text-amber-600 dark:text-amber-400";
        return "text-rose-600 dark:text-rose-400";
    };

    const getPerformanceBg = (percentage: number): string => {
        if (percentage >= 80) return "bg-emerald-50 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-800";
        if (percentage >= 60) return "bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800";
        if (percentage >= 40) return "bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800";
        return "bg-rose-50 dark:bg-rose-950/30 border-rose-200 dark:border-rose-800";
    };

    const getPerformanceBadge = (percentage: number): string => {
        if (percentage >= 80) return "bg-emerald-100 dark:bg-emerald-900/40 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-700";
        if (percentage >= 60) return "bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700";
        if (percentage >= 40) return "bg-amber-100 dark:bg-amber-900/40 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-700";
        return "bg-rose-100 dark:bg-rose-900/40 text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-700";
    };

    return (
        <div className="space-y-6">
            {/* Overall Performance */}
            <div className="bg-card border border-border rounded-xl p-6 shadow-sm">
                <h2 className="text-xl font-semibold mb-6 text-foreground">Overall Performance</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center p-4 bg-accent/30 rounded-lg">
                        <p className="text-sm font-medium text-muted-foreground mb-2">Total Score</p>
                        <p className="text-3xl font-bold text-foreground">
                            {evaluationData.totalMarks}/{evaluationData.maxMarks}
                        </p>
                    </div>
                    <div className="text-center p-4 bg-accent/30 rounded-lg">
                        <p className="text-sm font-medium text-muted-foreground mb-2">Percentage</p>
                        <p className={`text-3xl font-bold ${getPerformanceColor(evaluationData.overallPercentage)}`}>
                            {evaluationData.overallPercentage}%
                        </p>
                    </div>
                    <div className="text-center p-4 bg-accent/30 rounded-lg">
                        <p className="text-sm font-medium text-muted-foreground mb-2">Questions</p>
                        <p className="text-3xl font-bold text-foreground">{evaluationData.questions.length}</p>
                    </div>
                </div>
            </div>

            {/* Individual Question Breakdown */}
            <div className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground">Question-wise Breakdown</h2>
                {evaluationData.questions.map((question) => (
                    <div key={question.questionNumber} className="bg-card border border-border rounded-xl overflow-hidden shadow-sm">
                        {/* Question Header */}
                        <div className={`p-4 border-b ${getPerformanceBg(question.percentage)}`}>
                            <div className="flex justify-between items-center">
                                <div className="flex items-center gap-3">
                                    <h3 className="text-lg font-semibold text-foreground">
                                        {question.questionNumber}
                                    </h3>
                                    <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getPerformanceBadge(question.percentage)}`}>
                                        {question.percentage}%
                                    </span>
                                </div>
                                <div className="text-right">
                                    <p className={`text-lg font-bold ${getPerformanceColor(question.percentage)}`}>
                                        {question.marksAwarded}/{question.marksPossible}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                        marks
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Question Content */}
                        <div className="p-4 space-y-4">
                            {/* Criteria Breakdown */}
                            {question.criteriaBreakdown.length > 0 && (
                                <div>
                                    <h4 className="font-semibold mb-3 text-foreground flex items-center gap-2">
                                        <span className="w-1 h-4 bg-primary rounded-full"></span>
                                        Marking Criteria
                                    </h4>
                                    <div className="space-y-3">
                                        {question.criteriaBreakdown.map((criterion, criterionIndex) => {
                                            const criterionPercentage = criterion.maxScore ?
                                                Math.round((parseInt(criterion.score) / parseInt(criterion.maxScore)) * 100) : 0;

                                            return (
                                                <div key={criterionIndex} className="flex items-center justify-between p-4 bg-accent/20 dark:bg-accent/10 rounded-lg border border-border/50">
                                                    <span className="text-sm font-medium text-foreground flex-1 pr-4">
                                                        {criterion.criterion}
                                                    </span>
                                                    <div className="flex items-center gap-3">
                                                        <span className="font-semibold text-foreground text-sm">
                                                            {criterion.score}/{criterion.maxScore}
                                                        </span>
                                                        <span className={`text-xs px-2.5 py-1 rounded-full font-medium border ${getPerformanceBadge(criterionPercentage)}`}>
                                                            {criterionPercentage}%
                                                        </span>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}

                            {/* Feedback */}
                            {question.feedback && (
                                <div>
                                    <h4 className="font-semibold mb-3 text-foreground flex items-center gap-2">
                                        <span className="w-1 h-4 bg-blue-500 rounded-full"></span>
                                        Detailed Feedback
                                    </h4>
                                    <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                                        <p className="text-sm text-foreground leading-relaxed">
                                            {question.feedback}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default QuestionBreakdownDisplay;
